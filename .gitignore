*.java.hsp
*.sonarj
*.sw*
.DS_Store
.settings
.springBeans
bin
build.sh
integration-repo
ivy-cache
jxl.log
jmx.log
derby.log
spring-test/test-output/
.gradle
argfile*
pom.xml
activemq-data/

classes/
/build
buildSrc/build
/spring-*/build
/src/asciidoc/build
target/
/logs

# Eclipse artifacts, including WTP generated manifests
.classpath
.project
spring-*/src/main/java/META-INF/MANIFEST.MF

# IDEA artifacts and output dirs
*.iml
*.ipr
*.iws
.idea
out
test-output
atlassian-ide-plugin.xml
.gradletasknamecache
application-local.properties
application-uat.properties
application-dev.properties
application-beta.properties
src/main/resources/application-loc.properties
.run/beta.run.xml
gradlew
gradlew.bat
